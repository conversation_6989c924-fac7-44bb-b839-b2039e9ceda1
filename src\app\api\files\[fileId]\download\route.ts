import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { File } from '@/models/File';
import { S3Service } from '@/lib/s3';
import connectDB from '@/lib/mongodb';

export async function GET(
  request: NextRequest,
  { params }: { params: { fileId: string } }
) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { fileId } = params;

    await connectDB();

    // Find file and verify ownership
    const file = await File.findOne({
      _id: fileId,
      userId: session.user.id,
    });

    if (!file) {
      return NextResponse.json(
        { error: 'File not found or access denied' },
        { status: 404 }
      );
    }

    // Get S3 service instance
    let s3Service: S3Service;
    
    const s3Cookie = request.cookies.get('s3-credentials');
    
    if (s3Cookie) {
      try {
        s3Service = S3Service.fromEncryptedCookie(s3Cookie.value);
      } catch (error) {
        return NextResponse.json(
          { error: 'Invalid S3 credentials. Please reconfigure your storage settings.' },
          { status: 400 }
        );
      }
    } else if (session.user.allowPlatformS3) {
      s3Service = S3Service.getPlatformS3();
    } else {
      return NextResponse.json(
        { error: 'No S3 credentials configured' },
        { status: 400 }
      );
    }

    // Generate presigned download URL
    const downloadUrl = await s3Service.getPresignedDownloadUrl(file.s3Key);

    // Increment download count
    await file.incrementDownloadCount();

    return NextResponse.json({
      downloadUrl,
      fileName: file.name,
      originalName: file.originalName,
      size: file.size,
      mimeType: file.mimeType,
    });

  } catch (error) {
    console.error('Download URL generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate download URL' },
      { status: 500 }
    );
  }
}
