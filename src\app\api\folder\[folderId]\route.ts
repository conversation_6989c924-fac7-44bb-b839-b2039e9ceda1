import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { auth } from '@/lib/auth';
import { Folder } from '@/models/Folder';
import { File } from '@/models/File';
import { S3Service } from '@/lib/s3';
import connectDB from '@/lib/mongodb';

const renameFolderSchema = z.object({
  name: z.string().min(1, 'Folder name is required').max(255, 'Folder name too long'),
});

export async function GET(
  request: NextRequest,
  { params }: { params: { folderId: string } }
) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { folderId } = params;

    await connectDB();

    // Get folder details
    const folder = await Folder.findOne({
      _id: folderId,
      userId: session.user.id,
    });

    if (!folder) {
      return NextResponse.json(
        { error: 'Folder not found or access denied' },
        { status: 404 }
      );
    }

    // Get subfolders
    const subfolders = await Folder.find({
      parentFolderId: folderId,
      userId: session.user.id,
    }).sort({ name: 1 });

    // Get files in folder
    const files = await File.find({
      folderId: folderId,
      userId: session.user.id,
    }).sort({ name: 1 });

    return NextResponse.json({
      folder: {
        id: folder._id,
        name: folder.name,
        path: folder.path,
        parentFolderId: folder.parentFolderId,
        createdAt: folder.createdAt,
        updatedAt: folder.updatedAt,
      },
      subfolders: subfolders.map(f => ({
        id: f._id,
        name: f.name,
        path: f.path,
        createdAt: f.createdAt,
        updatedAt: f.updatedAt,
      })),
      files: files.map(f => ({
        id: f._id,
        name: f.name,
        originalName: f.originalName,
        size: f.size,
        mimeType: f.mimeType,
        isPublic: f.isPublic,
        downloadCount: f.downloadCount,
        createdAt: f.createdAt,
        updatedAt: f.updatedAt,
        tags: f.tags,
        description: f.description,
      })),
    });

  } catch (error) {
    console.error('Folder fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch folder' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { folderId: string } }
) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { folderId } = params;
    const body = await request.json();
    const { name } = renameFolderSchema.parse(body);

    await connectDB();

    // Find folder and verify ownership
    const folder = await Folder.findOne({
      _id: folderId,
      userId: session.user.id,
    });

    if (!folder) {
      return NextResponse.json(
        { error: 'Folder not found or access denied' },
        { status: 404 }
      );
    }

    // Check if folder with same name already exists in the same parent
    const existingFolder = await Folder.findOne({
      name,
      userId: session.user.id,
      parentFolderId: folder.parentFolderId,
      _id: { $ne: folderId },
    });

    if (existingFolder) {
      return NextResponse.json(
        { error: 'A folder with this name already exists in this location' },
        { status: 400 }
      );
    }

    // Update folder
    folder.name = name;
    await folder.save();

    return NextResponse.json({
      message: 'Folder renamed successfully',
      folder: {
        id: folder._id,
        name: folder.name,
        path: folder.path,
        parentFolderId: folder.parentFolderId,
        createdAt: folder.createdAt,
        updatedAt: folder.updatedAt,
      },
    });

  } catch (error) {
    console.error('Folder rename error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to rename folder' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { folderId: string } }
) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { folderId } = params;

    await connectDB();

    // Find folder and verify ownership
    const folder = await Folder.findOne({
      _id: folderId,
      userId: session.user.id,
    });

    if (!folder) {
      return NextResponse.json(
        { error: 'Folder not found or access denied' },
        { status: 404 }
      );
    }

    // Get S3 service for file deletion
    let s3Service: S3Service | null = null;
    const s3Cookie = request.cookies.get('s3-credentials');
    
    if (s3Cookie) {
      try {
        s3Service = S3Service.fromEncryptedCookie(s3Cookie.value);
      } catch (error) {
        // Continue without S3 service - files will remain in S3 but be orphaned
        console.error('Failed to initialize S3 service for cleanup:', error);
      }
    } else if (session.user.allowPlatformS3) {
      s3Service = S3Service.getPlatformS3();
    }

    // Recursively delete all subfolders and files
    await deleteFolderRecursively(folderId, session.user.id, s3Service);

    return NextResponse.json({
      message: 'Folder deleted successfully',
    });

  } catch (error) {
    console.error('Folder deletion error:', error);
    return NextResponse.json(
      { error: 'Failed to delete folder' },
      { status: 500 }
    );
  }
}

async function deleteFolderRecursively(folderId: string, userId: string, s3Service: S3Service | null) {
  // Get all subfolders
  const subfolders = await Folder.find({ parentFolderId: folderId, userId });
  
  // Recursively delete subfolders
  for (const subfolder of subfolders) {
    await deleteFolderRecursively(subfolder._id.toString(), userId, s3Service);
  }

  // Get all files in this folder
  const files = await File.find({ folderId, userId });
  
  // Delete files from S3 and database
  for (const file of files) {
    if (s3Service) {
      try {
        await s3Service.deleteFile(file.s3Key);
      } catch (error) {
        console.error(`Failed to delete file ${file.s3Key} from S3:`, error);
      }
    }
    await File.deleteOne({ _id: file._id });
  }

  // Delete the folder itself
  await Folder.deleteOne({ _id: folderId });
}
