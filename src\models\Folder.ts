import mongoose, { Document, Schema } from 'mongoose';

export interface IFolder extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  userId: mongoose.Types.ObjectId;
  parentFolderId?: mongoose.Types.ObjectId;
  path: string; // Full path for easy querying
  createdAt: Date;
  updatedAt: Date;
}

const FolderSchema = new Schema<IFolder>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 255,
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  parentFolderId: {
    type: Schema.Types.ObjectId,
    ref: 'Folder',
    required: false,
  },
  path: {
    type: String,
    required: true,
    trim: true,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
}, {
  timestamps: true,
});

// Indexes
FolderSchema.index({ userId: 1, parentFolderId: 1 });
FolderSchema.index({ userId: 1, path: 1 });
FolderSchema.index({ userId: 1, name: 1, parentFolderId: 1 }, { unique: true });

// Pre-save middleware to update path
FolderSchema.pre('save', async function(next) {
  try {
    if (this.isNew || this.isModified('name') || this.isModified('parentFolderId')) {
      if (this.parentFolderId) {
        // Use this.constructor to avoid circular reference issues
        const parentFolder = await (this.constructor as any).findById(this.parentFolderId);
        if (parentFolder) {
          this.path = `${parentFolder.path}/${this.name}`;
        } else {
          this.path = `/${this.name}`;
        }
      } else {
        this.path = `/${this.name}`;
      }
    }
    next();
  } catch (error) {
    next(error);
  }
});

// Methods
FolderSchema.methods.getSubfolders = function() {
  return mongoose.model('Folder').find({ parentFolderId: this._id });
};

FolderSchema.methods.getFiles = function() {
  return mongoose.model('File').find({ folderId: this._id });
};

FolderSchema.methods.getFullPath = function() {
  return this.path;
};

export const Folder = mongoose.models.Folder || mongoose.model<IFolder>('Folder', FolderSchema);
