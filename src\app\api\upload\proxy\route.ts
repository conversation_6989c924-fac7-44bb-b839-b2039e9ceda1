import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { auth } from '@/lib/auth';
import { S3Service } from '@/lib/s3';

const proxyUploadSchema = z.object({
  fileKey: z.string().min(1, 'File key is required'),
  contentType: z.string().min(1, 'Content type is required'),
});

export async function PUT(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get query parameters
    const url = new URL(request.url);
    const fileKey = url.searchParams.get('fileKey');
    const contentType = url.searchParams.get('contentType');

    if (!fileKey || !contentType) {
      return NextResponse.json(
        { error: 'Missing fileKey or contentType parameters' },
        { status: 400 }
      );
    }

    // Validate parameters
    const { fileKey: validatedFile<PERSON>ey, contentType: validatedContentType } = proxyUploadSchema.parse({
      fileKey,
      contentType,
    });

    // Get S3 service instance
    let s3Service: S3Service;
    
    const s3Cookie = request.cookies.get('s3-credentials');
    
    if (s3Cookie) {
      try {
        s3Service = S3Service.fromEncryptedCookie(s3Cookie.value);
      } catch (error) {
        return NextResponse.json(
          { error: 'Invalid S3 credentials. Please reconfigure your storage settings.' },
          { status: 400 }
        );
      }
    } else if (session.user.allowPlatformS3) {
      s3Service = S3Service.getPlatformS3();
    } else {
      return NextResponse.json(
        { error: 'No S3 credentials configured. Please configure your storage settings.' },
        { status: 400 }
      );
    }

    // Get file data from request body
    const fileBuffer = await request.arrayBuffer();
    
    if (!fileBuffer || fileBuffer.byteLength === 0) {
      return NextResponse.json(
        { error: 'No file data provided' },
        { status: 400 }
      );
    }

    // Upload to S3 directly from server
    await s3Service.uploadFile(
      validatedFileKey,
      new Uint8Array(fileBuffer),
      validatedContentType
    );

    return NextResponse.json({
      message: 'File uploaded successfully',
      fileKey: validatedFileKey,
    });

  } catch (error) {
    console.error('Proxy upload error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to upload file' },
      { status: 500 }
    );
  }
}
