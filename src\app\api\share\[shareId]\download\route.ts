import { NextRequest, NextResponse } from 'next/server';
import { Share } from '@/models/Share';
import { File } from '@/models/File';
import { S3Service } from '@/lib/s3';
import connectDB from '@/lib/mongodb';

export async function GET(
  request: NextRequest,
  { params }: { params: { shareId: string } }
) {
  try {
    const { shareId } = params;

    await connectDB();

    // Find active share
    const share = await Share.findActiveShare(shareId);

    if (!share) {
      return NextResponse.json(
        { error: 'Share not found or has expired' },
        { status: 404 }
      );
    }

    // Check if share can be accessed and allows downloads
    const canAccess = share.isActive &&
      (!share.expiresAt || new Date() < share.expiresAt) &&
      (!share.maxAccessCount || share.accessCount < share.maxAccessCount);

    if (!canAccess) {
      return NextResponse.json(
        { error: 'Share has expired or reached maximum access limit' },
        { status: 403 }
      );
    }

    if (!share.allowDownload) {
      return NextResponse.json(
        { error: 'Downloads are not allowed for this share' },
        { status: 403 }
      );
    }

    // Get file details
    const file = await (File as any).findById(share.fileId);
    if (!file) {
      return NextResponse.json(
        { error: 'File not found' },
        { status: 404 }
      );
    }

    // Get S3 service - try to use the file owner's credentials
    let s3Service: S3Service;
    
    // For shared files, we need to use the platform S3 or the owner's credentials
    // Since we can't access the owner's encrypted cookies, we'll use platform S3
    try {
      s3Service = S3Service.getPlatformS3();
    } catch (error) {
      return NextResponse.json(
        { error: 'File storage service unavailable' },
        { status: 503 }
      );
    }

    // Generate presigned download URL
    const downloadUrl = await s3Service.getPresignedDownloadUrl(file.s3Key);

    // Increment file download count
    await file.incrementDownloadCount();

    return NextResponse.json({
      downloadUrl,
      fileName: file.name,
      originalName: file.originalName,
      size: file.size,
      mimeType: file.mimeType,
    });

  } catch (error) {
    console.error('Shared file download error:', error);
    return NextResponse.json(
      { error: 'Failed to generate download URL' },
      { status: 500 }
    );
  }
}
