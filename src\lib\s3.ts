import { S3<PERSON>lient, <PERSON>BucketsCommand, PutO<PERSON>Command, GetObjectCommand, DeleteObjectCommand } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';
import { S3Credentials, decryptS3Credentials } from './encryption';

export class S3Service {
  private client: S3Client;
  private bucket: string;

  constructor(credentials: S3Credentials) {
    this.client = new S3Client({
      endpoint: credentials.endpoint,
      region: credentials.region,
      credentials: {
        accessKeyId: credentials.accessKeyId,
        secretAccessKey: credentials.secretAccessKey,
      },
      forcePathStyle: true, // Required for some S3-compatible services
    });
    this.bucket = credentials.bucket || 'drivn-files';
  }

  static fromEncryptedCookie(encryptedCredentials: string): S3Service {
    const credentials = decryptS3Credentials(encryptedCredentials);
    return new S3Service(credentials);
  }

  static getPlatformS3(): S3Service {
    const credentials: S3Credentials = {
      endpoint: process.env.PLATFORM_S3_ENDPOINT!,
      region: process.env.PLATFORM_S3_REGION!,
      accessKeyId: process.env.PLATFORM_S3_ACCESS_KEY_ID!,
      secretAccessKey: process.env.PLATFORM_S3_SECRET_ACCESS_KEY!,
      bucket: process.env.PLATFORM_S3_BUCKET!,
    };
    return new S3Service(credentials);
  }

  async validateCredentials(): Promise<boolean> {
    try {
      await this.client.send(new ListBucketsCommand({}));
      return true;
    } catch (error) {
      console.error('S3 validation error:', error);
      return false;
    }
  }

  async getPresignedUploadUrl(key: string, contentType: string): Promise<string> {
    const command = new PutObjectCommand({
      Bucket: this.bucket,
      Key: key,
      ContentType: contentType,
    });

    return await getSignedUrl(this.client, command, { expiresIn: 3600 }); // 1 hour
  }

  async getPresignedDownloadUrl(key: string): Promise<string> {
    const command = new GetObjectCommand({
      Bucket: this.bucket,
      Key: key,
    });

    return await getSignedUrl(this.client, command, { expiresIn: 3600 }); // 1 hour
  }

  async deleteFile(key: string): Promise<void> {
    const command = new DeleteObjectCommand({
      Bucket: this.bucket,
      Key: key,
    });

    await this.client.send(command);
  }

  generateFileKey(userId: string, fileName: string): string {
    const timestamp = Date.now();
    const sanitizedFileName = fileName.replace(/[^a-zA-Z0-9.-]/g, '_');
    return `users/${userId}/${timestamp}-${sanitizedFileName}`;
  }

  async uploadFile(key: string, body: Uint8Array, contentType: string): Promise<void> {
    const command = new PutObjectCommand({
      Bucket: this.bucket,
      Key: key,
      Body: body,
      ContentType: contentType,
    });

    await this.client.send(command);
  }

  getBucket(): string {
    return this.bucket;
  }
}
