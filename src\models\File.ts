import mongoose, { Document, Schema } from 'mongoose';

export interface IFile extends Document {
  _id: mongoose.Types.ObjectId;
  name: string;
  originalName: string;
  size: number;
  mimeType: string;
  s3Key: string;
  userId: mongoose.Types.ObjectId;
  folderId?: mongoose.Types.ObjectId;
  isPublic: boolean;
  downloadCount: number;
  createdAt: Date;
  updatedAt: Date;
  lastAccessedAt?: Date;
  tags?: string[];
  description?: string;
}

const FileSchema = new Schema<IFile>({
  name: {
    type: String,
    required: true,
    trim: true,
    maxlength: 255,
  },
  originalName: {
    type: String,
    required: true,
    trim: true,
    maxlength: 255,
  },
  size: {
    type: Number,
    required: true,
    min: 0,
  },
  mimeType: {
    type: String,
    required: true,
    trim: true,
  },
  s3Key: {
    type: String,
    required: true,
    unique: true,
  },
  userId: {
    type: Schema.Types.ObjectId,
    ref: 'User',
    required: true,
  },
  folderId: {
    type: Schema.Types.ObjectId,
    ref: 'Folder',
    required: false,
  },
  isPublic: {
    type: Boolean,
    default: false,
  },
  downloadCount: {
    type: Number,
    default: 0,
  },
  createdAt: {
    type: Date,
    default: Date.now,
  },
  updatedAt: {
    type: Date,
    default: Date.now,
  },
  lastAccessedAt: {
    type: Date,
    required: false,
  },
  tags: [{
    type: String,
    trim: true,
    maxlength: 50,
  }],
  description: {
    type: String,
    trim: true,
    maxlength: 500,
  },
}, {
  timestamps: true,
});

// Indexes
FileSchema.index({ userId: 1, folderId: 1 });
FileSchema.index({ userId: 1, name: 1 });
FileSchema.index({ isPublic: 1 });
FileSchema.index({ createdAt: -1 });
FileSchema.index({ tags: 1 });

// Methods
FileSchema.methods.incrementDownloadCount = function() {
  this.downloadCount += 1;
  this.lastAccessedAt = new Date();
  return this.save();
};

FileSchema.methods.getFileExtension = function() {
  return this.name.split('.').pop()?.toLowerCase() || '';
};

FileSchema.methods.isImage = function() {
  const imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg'];
  return imageTypes.includes(this.getFileExtension());
};

FileSchema.methods.isVideo = function() {
  const videoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'];
  return videoTypes.includes(this.getFileExtension());
};

FileSchema.methods.isAudio = function() {
  const audioTypes = ['mp3', 'wav', 'flac', 'aac', 'ogg'];
  return audioTypes.includes(this.getFileExtension());
};

export const File = mongoose.models.File || mongoose.model<IFile>('File', FileSchema);
