import Link from 'next/link';
import { But<PERSON> } from '@/components/ui/Button';
import { HomeIcon, ArrowLeftIcon } from '@heroicons/react/24/outline';

export default function NotFound() {
  return (
    <div className="min-h-screen bg-background flex items-center justify-center px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full text-center">
        {/* Logo */}
        <div className="flex justify-center mb-8">
          <div className="flex items-center space-x-2">
            <div className="w-10 h-10 bg-accent rounded-lg flex items-center justify-center">
              <span className="text-white font-bold text-xl">D</span>
            </div>
            <span className="text-2xl font-bold text-text-primary">Drivn</span>
          </div>
        </div>

        {/* 404 Content */}
        <div className="space-y-6">
          <div>
            <h1 className="text-6xl font-bold text-accent mb-4">404</h1>
            <h2 className="text-2xl font-bold text-text-primary mb-2">
              Page Not Found
            </h2>
            <p className="text-text-secondary">
              The page you're looking for doesn't exist or has been moved.
            </p>
          </div>

          {/* Actions */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/">
              <Button className="w-full sm:w-auto">
                <HomeIcon className="h-4 w-4 mr-2" />
                Go Home
              </Button>
            </Link>
            <Button
              variant="secondary"
              onClick={() => window.history.back()}
              className="w-full sm:w-auto"
            >
              <ArrowLeftIcon className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </div>
        </div>

        {/* Help Text */}
        <div className="mt-12 text-sm text-text-secondary">
          <p>
            If you believe this is an error, please{' '}
            <a
              href="https://github.com/Emmraan/drivn/issues"
              className="text-accent hover:text-accent/80 underline"
            >
              report issue
            </a>
            .
          </p>
        </div>
      </div>
    </div>
  );
}
