import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { auth } from '@/lib/auth';
import { S3Service } from '@/lib/s3';
import { isAllowedFileType, isValidFileSize, isValidFilename, sanitizeInput } from '@/lib/security';

const uploadUrlSchema = z.object({
  fileName: z.string().min(1, 'File name is required'),
  contentType: z.string().min(1, 'Content type is required'),
  fileSize: z.number().min(1, 'File size must be greater than 0'),
});



export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { fileName, contentType, fileSize } = uploadUrlSchema.parse(body);

    // Sanitize filename
    const sanitizedFileName = sanitizeInput(fileName);

    // Validate filename
    if (!isValidFilename(sanitizedFileName)) {
      return NextResponse.json(
        { error: 'Invalid filename. Please use a valid filename without special characters.' },
        { status: 400 }
      );
    }

    // Validate file type
    if (!isAllowedFileType(contentType)) {
      return NextResponse.json(
        { error: 'File type not allowed. Please upload a supported file type.' },
        { status: 400 }
      );
    }

    // Validate file size
    if (!isValidFileSize(fileSize)) {
      return NextResponse.json(
        { error: 'File size exceeds maximum limit of 100MB' },
        { status: 400 }
      );
    }

    // Get S3 service instance
    let s3Service: S3Service;
    
    const s3Cookie = request.cookies.get('s3-credentials');
    
    if (s3Cookie) {
      // Use user's S3 credentials
      try {
        s3Service = S3Service.fromEncryptedCookie(s3Cookie.value);
      } catch (error) {
        return NextResponse.json(
          { error: 'Invalid S3 credentials. Please reconfigure your storage settings.' },
          { status: 400 }
        );
      }
    } else if (session.user.allowPlatformS3) {
      // Use platform S3 as fallback
      s3Service = S3Service.getPlatformS3();
    } else {
      return NextResponse.json(
        { error: 'No S3 credentials configured. Please configure your storage settings.' },
        { status: 400 }
      );
    }

    // Generate file key
    const fileKey = s3Service.generateFileKey(session.user.id, sanitizedFileName);

    // Generate presigned upload URL
    const uploadUrl = await s3Service.getPresignedUploadUrl(fileKey, contentType);

    return NextResponse.json({
      uploadUrl,
      fileKey,
      fileName,
      contentType,
      fileSize,
    });

  } catch (error) {
    console.error('Upload URL generation error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to generate upload URL' },
      { status: 500 }
    );
  }
}
