import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { auth } from '@/lib/auth';
import { File } from '@/models/File';
import { Folder } from '@/models/Folder';
import { S3Service } from '@/lib/s3';
import connectDB from '@/lib/mongodb';

const updateFileSchema = z.object({
  name: z.string().min(1, 'File name is required').max(255, 'File name too long').optional(),
  description: z.string().max(500, 'Description too long').optional(),
  tags: z.array(z.string().max(50, 'Tag too long')).max(10, 'Too many tags').optional(),
  folderId: z.string().optional(),
});

export async function GET(
  request: NextRequest,
  { params }: { params: { fileId: string } }
) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { fileId } = params;

    await connectDB();

    // Find file and verify ownership
    const file = await File.findOne({
      _id: fileId,
      userId: session.user.id,
    }).populate('folderId');

    if (!file) {
      return NextResponse.json(
        { error: 'File not found or access denied' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      file: {
        id: file._id,
        name: file.name,
        originalName: file.originalName,
        size: file.size,
        mimeType: file.mimeType,
        isPublic: file.isPublic,
        downloadCount: file.downloadCount,
        createdAt: file.createdAt,
        updatedAt: file.updatedAt,
        lastAccessedAt: file.lastAccessedAt,
        tags: file.tags,
        description: file.description,
        folderId: file.folderId,
        folder: file.folderId ? {
          id: file.folderId._id,
          name: file.folderId.name,
          path: file.folderId.path,
        } : null,
      },
    });

  } catch (error) {
    console.error('File fetch error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch file' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { fileId: string } }
) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { fileId } = params;
    const body = await request.json();
    const updates = updateFileSchema.parse(body);

    await connectDB();

    // Find file and verify ownership
    const file = await File.findOne({
      _id: fileId,
      userId: session.user.id,
    });

    if (!file) {
      return NextResponse.json(
        { error: 'File not found or access denied' },
        { status: 404 }
      );
    }

    // Validate folder if provided
    if (updates.folderId) {
      const folder = await Folder.findOne({
        _id: updates.folderId,
        userId: session.user.id,
      });

      if (!folder) {
        return NextResponse.json(
          { error: 'Target folder not found or access denied' },
          { status: 404 }
        );
      }
    }

    // Check for name conflicts if name is being changed
    if (updates.name && updates.name !== file.name) {
      const existingFile = await File.findOne({
        name: updates.name,
        userId: session.user.id,
        folderId: updates.folderId || file.folderId,
        _id: { $ne: fileId },
      });

      if (existingFile) {
        return NextResponse.json(
          { error: 'A file with this name already exists in the target location' },
          { status: 400 }
        );
      }
    }

    // Update file
    Object.assign(file, updates);
    await file.save();

    return NextResponse.json({
      message: 'File updated successfully',
      file: {
        id: file._id,
        name: file.name,
        originalName: file.originalName,
        size: file.size,
        mimeType: file.mimeType,
        isPublic: file.isPublic,
        downloadCount: file.downloadCount,
        createdAt: file.createdAt,
        updatedAt: file.updatedAt,
        lastAccessedAt: file.lastAccessedAt,
        tags: file.tags,
        description: file.description,
        folderId: file.folderId,
      },
    });

  } catch (error) {
    console.error('File update error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to update file' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { fileId: string } }
) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { fileId } = params;

    await connectDB();

    // Find file and verify ownership
    const file = await File.findOne({
      _id: fileId,
      userId: session.user.id,
    });

    if (!file) {
      return NextResponse.json(
        { error: 'File not found or access denied' },
        { status: 404 }
      );
    }

    // Get S3 service for file deletion
    let s3Service: S3Service | null = null;
    const s3Cookie = request.cookies.get('s3-credentials');
    
    if (s3Cookie) {
      try {
        s3Service = S3Service.fromEncryptedCookie(s3Cookie.value);
      } catch (error) {
        console.error('Failed to initialize S3 service:', error);
      }
    } else if (session.user.allowPlatformS3) {
      s3Service = S3Service.getPlatformS3();
    }

    // Delete file from S3
    if (s3Service) {
      try {
        await s3Service.deleteFile(file.s3Key);
      } catch (error) {
        console.error(`Failed to delete file ${file.s3Key} from S3:`, error);
        // Continue with database deletion even if S3 deletion fails
      }
    }

    // Delete file from database
    await File.deleteOne({ _id: fileId });

    return NextResponse.json({
      message: 'File deleted successfully',
    });

  } catch (error) {
    console.error('File deletion error:', error);
    return NextResponse.json(
      { error: 'Failed to delete file' },
      { status: 500 }
    );
  }
}
