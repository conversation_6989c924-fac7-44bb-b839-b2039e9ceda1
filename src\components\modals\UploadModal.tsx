'use client';

import React from 'react';
import { Modal } from '@/components/ui/Modal';
import { toast } from '@/components/ui/Toast';
import { CloudArrowUpIcon, XMarkIcon } from '@heroicons/react/24/outline';
import { cn, formatFileSize } from '@/lib/utils';

interface UploadFile {
  file: File;
  id: string;
  progress: number;
  status: 'pending' | 'uploading' | 'completed' | 'error';
  error?: string;
}

interface UploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  folderId?: string;
  onUploadComplete?: (files: any[]) => void;
}

export const UploadModal: React.FC<UploadModalProps> = ({
  isOpen,
  onClose,
  folderId,
  onUploadComplete,
}) => {
  const [files, setFiles] = React.useState<UploadFile[]>([]);
  const [isDragOver, setIsDragOver] = React.useState(false);
  const fileInputRef = React.useRef<HTMLInputElement>(null);

  const generateId = () => Math.random().toString(36).substring(2);

  const handleFileSelect = (selectedFiles: FileList | null) => {
    if (!selectedFiles) return;

    const newFiles: UploadFile[] = Array.from(selectedFiles).map(file => ({
      file,
      id: generateId(),
      progress: 0,
      status: 'pending',
    }));

    setFiles(prev => [...prev, ...newFiles]);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    handleFileSelect(e.dataTransfer.files);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const removeFile = (id: string) => {
    setFiles(prev => prev.filter(f => f.id !== id));
  };

  const uploadFile = async (uploadFile: UploadFile) => {
    try {
      // Update status to uploading
      setFiles(prev => prev.map(f => 
        f.id === uploadFile.id ? { ...f, status: 'uploading' as const } : f
      ));

      // Get upload URL
      const urlResponse = await fetch('/api/upload/url', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileName: uploadFile.file.name,
          contentType: uploadFile.file.type,
          fileSize: uploadFile.file.size,
        }),
      });

      if (!urlResponse.ok) {
        throw new Error('Failed to get upload URL');
      }

      const { uploadUrl, fileKey } = await urlResponse.json();

      // Upload file to S3
      const uploadResponse = await fetch(uploadUrl, {
        method: 'PUT',
        body: uploadFile.file,
        headers: {
          'Content-Type': uploadFile.file.type,
        },
      });

      if (!uploadResponse.ok) {
        throw new Error('Failed to upload file');
      }

      // Complete upload
      const completeResponse = await fetch('/api/upload/complete', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          fileKey,
          fileName: uploadFile.file.name,
          originalName: uploadFile.file.name,
          contentType: uploadFile.file.type,
          fileSize: uploadFile.file.size,
          folderId,
        }),
      });

      if (!completeResponse.ok) {
        throw new Error('Failed to complete upload');
      }

      const result = await completeResponse.json();

      // Update status to completed
      setFiles(prev => prev.map(f => 
        f.id === uploadFile.id ? { ...f, status: 'completed' as const, progress: 100 } : f
      ));

      return result.file;
    } catch (error) {
      console.error('Upload error:', error);
      setFiles(prev => prev.map(f => 
        f.id === uploadFile.id ? { 
          ...f, 
          status: 'error' as const, 
          error: error instanceof Error ? error.message : 'Upload failed'
        } : f
      ));
      throw error;
    }
  };

  const handleUploadAll = async () => {
    const pendingFiles = files.filter(f => f.status === 'pending');
    if (pendingFiles.length === 0) return;

    try {
      const uploadPromises = pendingFiles.map(uploadFile);
      const results = await Promise.allSettled(uploadPromises);
      
      const successful = results
        .filter((result): result is PromiseFulfilledResult<any> => result.status === 'fulfilled')
        .map(result => result.value);

      if (successful.length > 0) {
        toast.success(`Successfully uploaded ${successful.length} file(s)`);
        onUploadComplete?.(successful);
      }

      const failed = results.filter(result => result.status === 'rejected').length;
      if (failed > 0) {
        toast.error(`Failed to upload ${failed} file(s)`);
      }
    } catch (error) {
      toast.error('Upload failed');
    }
  };

  const handleClose = () => {
    setFiles([]);
    onClose();
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={handleClose}
      title="Upload Files"
      size="lg"
    >
      <div className="space-y-6">
        {/* Drop Zone */}
        <div
          className={cn(
            'border-2 border-dashed rounded-lg p-8 text-center transition-colors',
            isDragOver
              ? 'border-accent bg-accent/5'
              : 'border-divider hover:border-accent/50'
          )}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          <CloudArrowUpIcon className="h-12 w-12 text-text-secondary mx-auto mb-4" />
          <p className="text-lg font-medium text-text-primary mb-2">
            Drop files here or click to browse
          </p>
          <p className="text-sm text-text-secondary mb-4">
            Maximum file size: 100MB
          </p>
          <button
            onClick={() => fileInputRef.current?.click()}
            className="px-4 py-2 bg-secondary text-text-primary border border-divider rounded-lg hover:bg-secondary/80"
          >
            Choose Files
          </button>
          <input
            ref={fileInputRef}
            type="file"
            multiple
            className="hidden"
            onChange={(e) => handleFileSelect(e.target.files)}
          />
        </div>

        {/* File List */}
        {files.length > 0 && (
          <div className="space-y-3">
            <h3 className="text-sm font-medium text-text-primary">
              Files to upload ({files.length})
            </h3>
            <div className="max-h-60 overflow-y-auto space-y-2">
              {files.map((uploadFile) => (
                <div
                  key={uploadFile.id}
                  className="flex items-center space-x-3 p-3 bg-secondary rounded-lg"
                >
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium text-text-primary truncate">
                      {uploadFile.file.name}
                    </p>
                    <p className="text-xs text-text-secondary">
                      {formatFileSize(uploadFile.file.size)}
                    </p>
                    {uploadFile.status === 'error' && uploadFile.error && (
                      <p className="text-xs text-error mt-1">{uploadFile.error}</p>
                    )}
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {uploadFile.status === 'uploading' && (
                      <div className="w-16 bg-divider rounded-full h-2">
                        <div
                          className="bg-accent h-2 rounded-full transition-all"
                          style={{ width: `${uploadFile.progress}%` }}
                        />
                      </div>
                    )}
                    
                    {uploadFile.status === 'completed' && (
                      <span className="text-xs text-success">✓ Uploaded</span>
                    )}
                    
                    {uploadFile.status === 'error' && (
                      <span className="text-xs text-error">✗ Failed</span>
                    )}
                    
                    {uploadFile.status === 'pending' && (
                      <button
                        onClick={() => removeFile(uploadFile.id)}
                        className="p-1 hover:bg-background rounded"
                      >
                        <XMarkIcon className="h-4 w-4 text-text-secondary" />
                      </button>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Actions */}
        <div className="flex justify-end space-x-3">
          <button
            onClick={handleClose}
            className="px-4 py-2 bg-secondary text-text-primary border border-divider rounded-lg hover:bg-secondary/80"
          >
            Cancel
          </button>
          <button
            onClick={handleUploadAll}
            disabled={files.filter(f => f.status === 'pending').length === 0}
            className="px-4 py-2 bg-accent text-white rounded-lg hover:bg-accent/90 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Upload All
          </button>
        </div>
      </div>
    </Modal>
  );
};
