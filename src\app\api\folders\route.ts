import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { auth } from '@/lib/auth';
import { Folder } from '@/models/Folder';
import connectDB from '@/lib/mongodb';

const createFolderSchema = z.object({
  name: z.string().min(1, 'Folder name is required').max(255, 'Folder name too long'),
  parentFolderId: z.string().optional(),
});

export async function POST(request: NextRequest) {
  try {
    const session = await auth();
    if (!session) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { name, parentFolderId } = createFolderSchema.parse(body);

    await connectDB();

    // Validate parent folder if provided
    if (parentFolderId) {
      const parentFolder = await Folder.findOne({
        _id: parentFolderId,
        userId: session.user.id,
      });

      if (!parentFolder) {
        return NextResponse.json(
          { error: 'Parent folder not found or access denied' },
          { status: 404 }
        );
      }
    }

    // Check if folder with same name already exists in the same parent
    const existingFolder = await Folder.findOne({
      name,
      userId: session.user.id,
      parentFolderId: parentFolderId || null,
    });

    if (existingFolder) {
      return NextResponse.json(
        { error: 'A folder with this name already exists in this location' },
        { status: 400 }
      );
    }

    // Create folder
    const folder = await Folder.create({
      name,
      userId: session.user.id,
      parentFolderId: parentFolderId || undefined,
    });

    return NextResponse.json({
      message: 'Folder created successfully',
      folder: {
        id: folder._id,
        name: folder.name,
        path: folder.path,
        parentFolderId: folder.parentFolderId,
        createdAt: folder.createdAt,
        updatedAt: folder.updatedAt,
      },
    }, { status: 201 });

  } catch (error) {
    console.error('Folder creation error:', error);

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Validation error', details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Failed to create folder' },
      { status: 500 }
    );
  }
}
