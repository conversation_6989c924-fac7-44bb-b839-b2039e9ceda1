'use client';

import React from 'react';
import { FileCard } from '@/components/ui/FileCard';
import { SkeletonFileCard } from '@/components/ui/Skeleton';
import { useRouter } from 'next/navigation';
import { toast } from '@/components/ui/Toast';

interface File {
  id: string;
  name: string;
  size: number;
  mimeType: string;
  createdAt: string;
  downloadCount: number;
}

export default function FilesPage() {
  const [files, setFiles] = React.useState<File[]>([]);
  const [loading, setLoading] = React.useState(true);

  React.useEffect(() => {
    fetchFiles();
  }, []);

  const fetchFiles = async () => {
    setLoading(true);
    try {
      const res = await fetch('/api/files');
      if (!res.ok) throw new Error('Failed to fetch files');
      const data = await res.json();
      setFiles(data.files);
    } catch (error) {
      toast.error('Failed to load files');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async (fileId: string) => {
    try {
      const res = await fetch(`/api/files/${fileId}/download`);
      if (!res.ok) throw new Error('Failed to get download URL');
      const data = await res.json();
      window.open(data.downloadUrl, '_blank');
      toast.success('Download started');
    } catch (error) {
      toast.error('Failed to download file');
    }
  };

  const handleShare = (fileId: string, fileName: string) => {
    toast.info('Share functionality not implemented yet');
  };

  const handleDelete = async (fileId: string) => {
    if (!confirm('Are you sure you want to delete this file?')) return;
    try {
      const res = await fetch(`/api/files/${fileId}`, { method: 'DELETE' });
      if (!res.ok) throw new Error('Failed to delete file');
      toast.success('File deleted');
      fetchFiles();
    } catch (error) {
      toast.error('Failed to delete file');
    }
  };

  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold text-text-primary mb-4">My Files</h1>
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[...Array(6)].map((_, i) => (
            <SkeletonFileCard key={i} />
          ))}
        </div>
      ) : files.length === 0 ? (
        <p>No files found. Upload some files to get started.</p>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {files.map((file) => (
            <FileCard
              key={file.id}
              id={file.id}
              name={file.name}
              size={file.size}
              mimeType={file.mimeType}
              createdAt={new Date(file.createdAt)}
              downloadCount={file.downloadCount}
              type="file"
              onDownload={() => handleDownload(file.id)}
              onShare={() => handleShare(file.id, file.name)}
              onDelete={() => handleDelete(file.id)}
            />
          ))}
        </div>
      )}
    </div>
  );
}