'use client';

import React from 'react';
import { useSession } from 'next-auth/react';
import { FileCard } from '@/components/ui/FileCard';
import { SkeletonFileCard } from '@/components/ui/Skeleton';
import { UploadModal } from '@/components/modals/UploadModal';
import { ShareModal } from '@/components/modals/ShareModal';
import { CreateFolderModal } from '@/components/modals/CreateFolderModal';
import { toast } from '@/components/ui/Toast';
import {
  CloudArrowUpIcon,
  FolderPlusIcon,
  ChartBarIcon,
  DocumentIcon,
} from '@heroicons/react/24/outline';

interface DashboardStats {
  totalFiles: number;
  totalSize: number;
  totalFolders: number;
  recentUploads: number;
}

interface RecentFile {
  id: string;
  name: string;
  size: number;
  mimeType: string;
  createdAt: string;
  downloadCount: number;
}

export default function DashboardPage() {
  const { data: session } = useSession();
  const [stats, setStats] = React.useState<DashboardStats | null>(null);
  const [recentFiles, setRecentFiles] = React.useState<RecentFile[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [uploadModalOpen, setUploadModalOpen] = React.useState(false);
  const [shareModalOpen, setShareModalOpen] = React.useState(false);
  const [createFolderModalOpen, setCreateFolderModalOpen] = React.useState(false);
  const [selectedFile, setSelectedFile] = React.useState<{ id: string; name: string } | null>(null);

  React.useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    try {
      // Fetch recent files
      const filesResponse = await fetch('/api/files?limit=6&sortBy=createdAt&sortOrder=desc');
      if (filesResponse.ok) {
        const filesData = await filesResponse.json();
        setRecentFiles(filesData.files);
        
        // Calculate stats from files data
        setStats({
          totalFiles: filesData.pagination.totalCount,
          totalSize: filesData.files.reduce((sum: number, file: any) => sum + file.size, 0),
          totalFolders: 0,
          recentUploads: filesData.files.filter((file: any) => {
            const uploadDate = new Date(file.createdAt);
            const weekAgo = new Date();
            weekAgo.setDate(weekAgo.getDate() - 7);
            return uploadDate > weekAgo;
          }).length,
        });
      }
    } catch (error) {
      console.error('Failed to fetch dashboard data:', error);
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleFileDownload = async (fileId: string) => {
    try {
      const response = await fetch(`/api/files/${fileId}/download`);
      if (!response.ok) {
        throw new Error('Failed to get download URL');
      }
      
      const data = await response.json();
      window.open(data.downloadUrl, '_blank');
      toast.success('Download started');
    } catch (error) {
      toast.error('Failed to download file');
    }
  };

  const handleFileShare = (fileId: string, fileName: string) => {
    setSelectedFile({ id: fileId, name: fileName });
    setShareModalOpen(true);
  };

  const handleFileDelete = async (fileId: string) => {
    if (!confirm('Are you sure you want to delete this file?')) {
      return;
    }

    try {
      const response = await fetch(`/api/files/${fileId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete file');
      }

      toast.success('File deleted successfully');
      fetchDashboardData();
    } catch (error) {
      toast.error('Failed to delete file');
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="p-6 space-y-6">
      {/* Welcome Section */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-text-primary">
            Welcome back, {session?.user?.name}!
          </h1>
          <p className="text-text-secondary">
            Here's what's happening with your files today.
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={() => setUploadModalOpen(true)}
            className="flex items-center px-4 py-2 bg-accent text-white rounded-lg hover:bg-accent/90"
          >
            <CloudArrowUpIcon className="h-4 w-4 mr-2" />
            Upload Files
          </button>
          <button
            onClick={() => setCreateFolderModalOpen(true)}
            className="flex items-center px-4 py-2 bg-secondary text-text-primary border border-divider rounded-lg hover:bg-secondary/80"
          >
            <FolderPlusIcon className="h-4 w-4 mr-2" />
            New Folder
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-background border border-divider rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-2 bg-accent/10 rounded-lg">
              <DocumentIcon className="h-6 w-6 text-accent" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">Total Files</p>
              <p className="text-2xl font-bold text-text-primary">
                {loading ? '...' : stats?.totalFiles || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-background border border-divider rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-2 bg-success/10 rounded-lg">
              <ChartBarIcon className="h-6 w-6 text-success" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">Storage Used</p>
              <p className="text-2xl font-bold text-text-primary">
                {loading ? '...' : formatFileSize(stats?.totalSize || 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-background border border-divider rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-2 bg-warning/10 rounded-lg">
              <FolderPlusIcon className="h-6 w-6 text-warning" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">Folders</p>
              <p className="text-2xl font-bold text-text-primary">
                {loading ? '...' : stats?.totalFolders || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-background border border-divider rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-2 bg-accent/10 rounded-lg">
              <CloudArrowUpIcon className="h-6 w-6 text-accent" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">Recent Uploads</p>
              <p className="text-2xl font-bold text-text-primary">
                {loading ? '...' : stats?.recentUploads || 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Recent Files */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-text-primary">Recent Files</h2>
          <button className="px-3 py-1 text-sm text-text-primary hover:bg-secondary rounded-lg">
            View All
          </button>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Array.from({ length: 6 }).map((_, index) => (
              <SkeletonFileCard key={index} />
            ))}
          </div>
        ) : recentFiles.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {recentFiles.map((file) => (
              <FileCard
                key={file.id}
                id={file.id}
                name={file.name}
                type="file"
                size={file.size}
                mimeType={file.mimeType}
                createdAt={new Date(file.createdAt)}
                downloadCount={file.downloadCount}
                onDownload={() => handleFileDownload(file.id)}
                onShare={() => handleFileShare(file.id, file.name)}
                onDelete={() => handleFileDelete(file.id)}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <DocumentIcon className="h-12 w-12 text-text-secondary mx-auto mb-4" />
            <h3 className="text-lg font-medium text-text-primary mb-2">No files yet</h3>
            <p className="text-text-secondary mb-4">
              Upload your first file to get started with Drivn.
            </p>
          </div>
        )}
      </div>

      {/* Modals */}
      <UploadModal
        isOpen={uploadModalOpen}
        onClose={() => setUploadModalOpen(false)}
        onUploadComplete={() => {
          fetchDashboardData();
          setUploadModalOpen(false);
        }}
      />

      {selectedFile && (
        <ShareModal
          isOpen={shareModalOpen}
          onClose={() => {
            setShareModalOpen(false);
            setSelectedFile(null);
          }}
          fileId={selectedFile.id}
          fileName={selectedFile.name}
        />
      )}

      <CreateFolderModal
        isOpen={createFolderModalOpen}
        onClose={() => setCreateFolderModalOpen(false)}
        onFolderCreated={() => {
          fetchDashboardData();
          setCreateFolderModalOpen(false);
        }}
      />
    </div>
  );
}
