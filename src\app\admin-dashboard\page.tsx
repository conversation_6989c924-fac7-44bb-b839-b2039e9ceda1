'use client';

import React from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/components/ui/Button';
import { Input } from '@/components/ui/Input';
import { toast } from '@/components/ui/Toast';
import { SkeletonTable } from '@/components/ui/Skeleton';
import {
  UsersIcon,
  DocumentIcon,
  FolderIcon,
  ShareIcon,
  ChartBarIcon,
  MagnifyingGlassIcon,
  CheckCircleIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';
import { formatFileSize, formatDate } from '@/lib/utils';

interface AdminStats {
  overview: {
    totalUsers: number;
    totalFiles: number;
    totalFolders: number;
    totalShares: number;
    totalStorageUsed: number;
    avgFileSize: number;
  };
  users: {
    total: number;
    verified: number;
    unverified: number;
    platformS3Users: number;
    recentSignups: number;
  };
  files: {
    total: number;
    recentUploads: number;
    totalSize: number;
    avgSize: number;
  };
  shares: {
    total: number;
    recentShares: number;
  };
}

interface User {
  id: string;
  name: string;
  email: string;
  verified: boolean;
  allowPlatformS3: boolean;
  createdAt: string;
  lastLogin?: string;
  avatarUrl?: string;
}

export default function AdminDashboardPage() {
  const { data: session } = useSession();
  const [stats, setStats] = React.useState<AdminStats | null>(null);
  const [users, setUsers] = React.useState<User[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [usersLoading, setUsersLoading] = React.useState(true);
  const [searchQuery, setSearchQuery] = React.useState('');
  const [currentPage, setCurrentPage] = React.useState(1);
  const [totalPages, setTotalPages] = React.useState(1);

  React.useEffect(() => {
    fetchStats();
    fetchUsers();
  }, []);

  React.useEffect(() => {
    fetchUsers();
  }, [currentPage, searchQuery]);

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/admin/stats');
      if (!response.ok) {
        throw new Error('Failed to fetch stats');
      }
      const data = await response.json();
      setStats(data);
    } catch (error) {
      console.error('Failed to fetch admin stats:', error);
      toast.error('Failed to load admin statistics');
    } finally {
      setLoading(false);
    }
  };

  const fetchUsers = async () => {
    setUsersLoading(true);
    try {
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '10',
      });
      
      if (searchQuery) {
        params.append('search', searchQuery);
      }

      const response = await fetch(`/api/admin/users?${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }
      
      const data = await response.json();
      setUsers(data.users);
      setTotalPages(data.pagination.totalPages);
    } catch (error) {
      console.error('Failed to fetch users:', error);
      toast.error('Failed to load users');
    } finally {
      setUsersLoading(false);
    }
  };

  const handleTogglePlatformS3 = async (userId: string, currentValue: boolean) => {
    try {
      const response = await fetch('/api/admin/users', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          userId,
          allowPlatformS3: !currentValue,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update user');
      }

      toast.success('User updated successfully');
      fetchUsers();
      fetchStats();
    } catch (error) {
      console.error('Failed to update user:', error);
      toast.error('Failed to update user');
    }
  };

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchUsers();
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-secondary rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="h-24 bg-secondary rounded"></div>
            ))}
          </div>
          <SkeletonTable rows={10} columns={6} />
        </div>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-text-primary">Admin Dashboard</h1>
        <p className="text-text-secondary">
          Manage users and monitor platform usage
        </p>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <div className="bg-background border border-divider rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-2 bg-accent/10 rounded-lg">
              <UsersIcon className="h-6 w-6 text-accent" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">Total Users</p>
              <p className="text-2xl font-bold text-text-primary">
                {stats?.overview.totalUsers || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-background border border-divider rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-2 bg-success/10 rounded-lg">
              <DocumentIcon className="h-6 w-6 text-success" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">Total Files</p>
              <p className="text-2xl font-bold text-text-primary">
                {stats?.overview.totalFiles || 0}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-background border border-divider rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-2 bg-warning/10 rounded-lg">
              <ChartBarIcon className="h-6 w-6 text-warning" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">Storage Used</p>
              <p className="text-2xl font-bold text-text-primary">
                {formatFileSize(stats?.overview.totalStorageUsed || 0)}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-background border border-divider rounded-lg p-6">
          <div className="flex items-center">
            <div className="p-2 bg-accent/10 rounded-lg">
              <ShareIcon className="h-6 w-6 text-accent" />
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-text-secondary">Active Shares</p>
              <p className="text-2xl font-bold text-text-primary">
                {stats?.overview.totalShares || 0}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* User Management */}
      <div className="bg-background border border-divider rounded-lg">
        <div className="p-6 border-b border-divider">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-text-primary">User Management</h2>
            <form onSubmit={handleSearch} className="flex space-x-2">
              <Input
                type="text"
                placeholder="Search users..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                leftIcon={<MagnifyingGlassIcon className="h-4 w-4" />}
                className="w-64"
              />
              <Button type="submit" size="sm">
                Search
              </Button>
            </form>
          </div>
        </div>

        <div className="overflow-x-auto">
          {usersLoading ? (
            <div className="p-6">
              <SkeletonTable rows={10} columns={6} />
            </div>
          ) : (
            <table className="w-full">
              <thead className="bg-secondary/50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                    User
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Platform S3
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Joined
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Last Login
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-text-secondary uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-divider">
                {users.map((user) => (
                  <tr key={user.id} className="hover:bg-secondary/30">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center">
                        {user.avatarUrl ? (
                          <img
                            className="h-8 w-8 rounded-full"
                            src={user.avatarUrl}
                            alt={user.name}
                          />
                        ) : (
                          <div className="h-8 w-8 rounded-full bg-accent/10 flex items-center justify-center">
                            <span className="text-sm font-medium text-accent">
                              {user.name.charAt(0).toUpperCase()}
                            </span>
                          </div>
                        )}
                        <div className="ml-3">
                          <div className="text-sm font-medium text-text-primary">
                            {user.name}
                          </div>
                          <div className="text-sm text-text-secondary">
                            {user.email}
                          </div>
                        </div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span
                        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          user.verified
                            ? 'bg-success/10 text-success'
                            : 'bg-warning/10 text-warning'
                        }`}
                      >
                        {user.verified ? (
                          <>
                            <CheckCircleIcon className="h-3 w-3 mr-1" />
                            Verified
                          </>
                        ) : (
                          <>
                            <XCircleIcon className="h-3 w-3 mr-1" />
                            Unverified
                          </>
                        )}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <button
                        onClick={() => handleTogglePlatformS3(user.id, user.allowPlatformS3)}
                        className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                          user.allowPlatformS3 ? 'bg-accent' : 'bg-divider'
                        }`}
                      >
                        <span
                          className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                            user.allowPlatformS3 ? 'translate-x-6' : 'translate-x-1'
                          }`}
                        />
                      </button>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-text-secondary">
                      {formatDate(new Date(user.createdAt))}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-text-secondary">
                      {user.lastLogin ? formatDate(new Date(user.lastLogin)) : 'Never'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <Button variant="ghost" size="sm">
                        View Details
                      </Button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="px-6 py-3 border-t border-divider flex items-center justify-between">
            <div className="text-sm text-text-secondary">
              Page {currentPage} of {totalPages}
            </div>
            <div className="flex space-x-2">
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <Button
                variant="secondary"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
